import React from "react";
import { Metadata } from "next";

import HeroSection from "@/components/broken-ink/HeroSection";
import SocialMediaSection from "@/components/broken-ink/SocialMediaSection";
import ServicesSection from "@/components/broken-ink/ServicesSection";
import GallerySection from "@/components/broken-ink/GallerySection";
import ArtistsSection from "@/components/broken-ink/ArtistsSection";
import LocationSection from "@/components/broken-ink/LocationSection";
import ContactSection from "@/components/broken-ink/ContactSection";
import MobileFooter from "@/components/broken-ink/MobileFooter";

import { Noto_Sans, Space_Grotesk } from "next/font/google";

const notoSans = Noto_Sans({
  subsets: ["latin"],
  weight: ["400", "500", "700", "900"],
  variable: "--font-noto-sans",
  display: "swap",
});

const spaceGrotesk = Space_Grotesk({
  subsets: ["latin"],
  weight: ["400", "500", "700"],
  variable: "--font-space-grotesk",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Broken Ink Tattoo - Onde a Arte Encontra a Pele",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

export default function BrokenInkPage() {
  return (
    <div
      className={`relative flex size-full min-h-screen flex-col bg-black dark justify-between group/design-root overflow-x-hidden ${notoSans.variable} ${spaceGrotesk.variable} font-spaceGrotesk`}
    >
      <div>
        {/* <Header /> */}
        <main>
          <HeroSection />
          <SocialMediaSection />
          <ServicesSection />
          <GallerySection />
          <ArtistsSection />
          <LocationSection />
          <ContactSection />
        </main>
      </div>
      <MobileFooter />
    </div>
  );
}
