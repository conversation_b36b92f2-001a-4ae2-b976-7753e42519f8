import React from "react";
import GalleryImage from "./GalleryImage";
import { UserProfile } from "@/types/user";
import { transformGalleryData } from "@/lib/brokenInkUtils";
interface GallerySectionProps {
  profile: UserProfile;
}

const GallerySection = ({ profile }: GallerySectionProps) => {
  const galleryData = transformGalleryData(profile);

  if (!galleryData.enabled) {
    return null;
  }

  return (
    <section className="py-16 sm:py-24 bg-gray-900/50" id="gallery">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
            {galleryData.title}
          </h2>
          {galleryData.description && (
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              {galleryData.description}
            </p>
          )}
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {galleryData.galleryItems.map((item) => (
            <GalleryImage
              key={item.title}
              imageUrl={item.imageUrl}
              altText={item.altText}
              title={item.title}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default GallerySection;
