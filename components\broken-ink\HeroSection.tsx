import React from "react";
import { UserProfile } from "@/types/user";
import { transformHeroData } from "@/lib/brokenInkUtils";

interface HeroSectionProps {
  profile: UserProfile;
}

const HeroSection = ({ profile }: HeroSectionProps) => {
  const heroData = transformHeroData(profile);

  return (
    <section className="relative">
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: `url("${heroData.backgroundImage}")`,
        }}
      ></div>
      {/* Aggressive gradient overlay that fades from light to very dark at bottom */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/60 to-black/95"></div>
      {/* Strong bottom gradient for seamless blending with black component */}
      <div className="absolute bottom-0 left-0 right-0 h-64 bg-gradient-to-b from-transparent via-black/40 to-black"></div>
      <div className="relative container mx-auto flex min-h-[70vh] flex-col items-center justify-center gap-6 p-4 pb-16 text-center">
        <h1 className="text-white text-5xl font-black leading-tight tracking-[-0.033em] md:text-7xl">
          {heroData.title}
        </h1>
        <p className="max-w-xl text-lg font-light text-gray-200 md:text-xl">
          {heroData.description}
        </p>
        <button
          className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-6 bg-white text-black text-base font-bold leading-normal tracking-wide transition-transform hover:scale-105"
          style={
            heroData.colors?.primary
              ? {
                  backgroundColor: heroData.colors.primary,
                  color: heroData.colors.linkText || "#ffffff",
                }
              : {}
          }
        >
          <span className="truncate">{heroData.buttonText}</span>
        </button>
      </div>
    </section>
  );
};

export default HeroSection;
