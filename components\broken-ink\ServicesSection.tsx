import React from "react";
import ServiceCard from "./ServiceCard";
import { getIconComponent } from "@/lib/iconUtils";
import { UserProfile } from "@/types/user";
import { transformServicesData } from "@/lib/brokenInkUtils";

interface ServicesSectionProps {
  profile: UserProfile;
}

const ServicesSection = ({ profile }: ServicesSectionProps) => {
  const servicesData = transformServicesData(profile);

  if (!servicesData.enabled) {
    return null;
  }

  return (
    <section className="py-16 sm:py-24" id="services">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
            {servicesData.title}
          </h2>
          {servicesData.description && (
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              {servicesData.description}
            </p>
          )}
        </div>
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {servicesData.services.map((service) => {
            const IconComponent = getIconComponent(service.iconName);
            return (
              <ServiceCard
                key={service.title}
                icon={<IconComponent className="h-8 w-8" />}
                title={service.title}
                description={service.description}
              />
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
