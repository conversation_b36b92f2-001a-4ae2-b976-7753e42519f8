import { UserProfile } from '@/types/user'

// ============================================================================
// BROKEN INK DATA TRANSFORMATION UTILITIES
// ============================================================================

/**
 * Transform UserProfile data for HeroSection component
 */
export function transformHeroData(profile: UserProfile) {
  return {
    title: profile.user.name || 'Tattoo Studio',
    description: profile.user.bio || 'Onde a arte encontra a pele. Explore nossos designs únicos e artistas talentosos.',
    backgroundImage: profile.user.avatar || 'https://scontent.fbfh8-1.fna.fbcdn.net/v/t39.30808-6/476456513_937782661848653_6745617227350114204_n.jpg?_nc_cat=100&ccb=1-7&_nc_sid=cc71e4&_nc_ohc=e7sXJtmLNP4Q7kNvwELDazQ&_nc_oc=Admx34j2Tf_YoMuPLqX4cJ8MEZaKHVEPO8uO7wr5aK1Q4bHEKyVcyaKUHfWLjRMF0wF9EfFWOjZe-S1tcQ8sd6ns&_nc_zt=23&_nc_ht=scontent.fbfh8-1.fna&_nc_gid=x1sWburtgv1RfG-25TsNyQ&oh=00_AfR673nWeKRtmpW0wm8V5vX-UbAnjUoUvMtXdYQP9vAPOA&oe=68739539',
    buttonText: 'Agendar',
    colors: profile.settings?.colors
  }
}

/**
 * Transform UserProfile socialMedia for SocialMediaSection component
 */
export function transformSocialMediaData(profile: UserProfile) {
  const defaultPlatforms = [
    {
      iconName: "instagram",
      platformName: "Instagram",
      description: "Veja nosso portfólio e atualizações diárias.",
      href: "#",
    },
    {
      iconName: "facebook",
      platformName: "Facebook", 
      description: "Participe da nossa comunidade e eventos.",
      href: "#",
    },
    {
      iconName: "twitter",
      platformName: "Twitter",
      description: "Notícias rápidas e novidades do estúdio.",
      href: "#",
    },
  ]

  // Transform user's social media links
  const userSocialPlatforms = profile.socialMedia?.map(social => ({
    iconName: getIconNameFromUrl(social.url) || extractIconFromClass(social.classIcon),
    platformName: social.text,
    description: `Siga-nos no ${social.text}`,
    href: social.url,
  })) || []

  return {
    title: "Nos Siga nas Redes Sociais",
    description: "Acompanhe nossa jornada e inspire-se com nossos trabalhos mais recentes.",
    socialPlatforms: userSocialPlatforms.length > 0 ? userSocialPlatforms : defaultPlatforms,
    colors: profile.settings?.colors
  }
}

/**
 * Transform UserProfile servicesSection for ServicesSection component
 */
export function transformServicesData(profile: UserProfile) {
  const defaultServices = [
    {
      iconName: "brush",
      title: "Tatuagens Personalizadas",
      description: "Dê vida à sua visão com uma peça única desenhada por nossos talentosos artistas.",
    },
    {
      iconName: "palette",
      title: "Designs Prontos",
      description: "Escolha entre nossa coleção curada de designs pré-desenhados, prontos para serem tatuados.",
    },
    {
      iconName: "refresh",
      title: "Cover-Ups",
      description: "Expertly conceal or transform old tattoos with our creative cover-up solutions.",
    },
  ]

  // Use user's services if available and enabled
  const userServices = profile.servicesSection?.enabled && profile.servicesSection.items?.length > 0
    ? profile.servicesSection.items.map(service => ({
        iconName: service.icon || "brush",
        title: service.title,
        description: service.description,
      }))
    : defaultServices

  return {
    title: profile.servicesSection?.title || "Nossos Serviços",
    description: profile.servicesSection?.description || "Oferecemos uma ampla gama de serviços de tatuagem profissional.",
    services: userServices,
    enabled: profile.servicesSection?.enabled !== false,
    colors: profile.settings?.colors
  }
}

/**
 * Transform UserProfile gallery for GallerySection component
 */
export function transformGalleryData(profile: UserProfile) {
  const defaultGalleryItems = [
    {
      imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuA7w9N14rC0kpL-oKDNdXB2RBbbEt3bkkAiMUw90ilcNsio4NdErcvCW2QA-2GTo0gF6ll45gnFUEm0ISiGtfBJx_FPWaV_pyVQ9fjaqYtI1sbS0ZF9Nq_YQSudhoq1FFZf9ewMQSMDgJce2ILmSFKUuK-DSJdyJagpWJ46zn2WDJ1KDRBGK2CjR-jqKnRodrSmUDsppCbirxG8BzR1IL58nc1_FUpSTkfR9fHkON8tK6Zgu5alEcsQmsrPtSY-iEhJxTNkwrorqKM",
      altText: "Intricate Sleeve Tattoo",
      title: "Intricate Sleeve"
    },
  ]

  // Use user's gallery if available and enabled
  const userGalleryItems = profile.gallery?.enabled && profile.gallery.images?.length > 0
    ? profile.gallery.images.map(image => ({
        imageUrl: image.url,
        altText: image.alt || image.title || "Gallery Image",
        title: image.title || "Tattoo Work"
      }))
    : defaultGalleryItems

  return {
    title: profile.gallery?.title || "Nossa Galeria",
    description: profile.gallery?.description || "Explore nossos trabalhos mais recentes e inspire-se.",
    galleryItems: userGalleryItems,
    enabled: profile.gallery?.enabled !== false,
    colors: profile.settings?.colors
  }
}

/**
 * Transform UserProfile featuresSection for ArtistsSection component
 * (We repurpose featuresSection to represent artists)
 */
export function transformArtistsData(profile: UserProfile) {
  const defaultArtists = [
    {
      imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuD276eMEwr2iOqz4mmHWMuFUqDz7Fg5g-aXV5ePnr8XEPoE7SAcYMGhs2Q2JJHonU1zu12FN7-78c24bBLsyhVjlw2rLHtHzNlfi6yK5We_wRKlAHcFI1d0xxcrhy35KaYvxwpGGmG4TNbVTsvl4zp3x5WIKF3u407AqtvZhE_dLwT5afq8bNDCBr4i1SgvIZGRrQn_5Y_ERKS0JmWN_9S_tEJ5jr9o02sUwtDaWjDZUKmkRwSuOB-mBnbT3ONiS8JLzIpQDijmSZg",
      name: "Ethan Carter",
      specialty: "Specializes in realism"
    },
  ]

  // Use user's features as artists if available and enabled
  const userArtists = profile.featuresSection?.enabled && profile.featuresSection.items?.length > 0
    ? profile.featuresSection.items.map(feature => ({
        imageUrl: feature.image || profile.user.avatar,
        name: feature.title,
        specialty: feature.description || "Tattoo Artist"
      }))
    : defaultArtists

  return {
    title: profile.featuresSection?.title || "Nossos Artistas",
    artists: userArtists,
    enabled: profile.featuresSection?.enabled !== false,
    colors: profile.settings?.colors
  }
}

/**
 * Transform UserProfile location for LocationSection component
 */
export function transformLocationData(profile: UserProfile) {
  return {
    enabled: profile.location?.enabled || false,
    title: profile.location?.title || "Nossa Localização",
    description: profile.location?.description || "Venha nos visitar em nosso estabelecimento",
    address: profile.location?.address,
    coordinates: profile.location?.coordinates,
    colors: profile.settings?.colors
  }
}

/**
 * Transform UserProfile contact data for ContactSection component
 */
export function transformContactData(profile: UserProfile) {
  return {
    phone: profile.phone,
    links: profile.links || [],
    socialMedia: profile.socialMedia || [],
    colors: profile.settings?.colors
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Extract icon name from social media URL
 */
function getIconNameFromUrl(url: string): string | null {
  if (url.includes('instagram.com')) return 'instagram'
  if (url.includes('facebook.com')) return 'facebook'
  if (url.includes('twitter.com') || url.includes('x.com')) return 'twitter'
  if (url.includes('tiktok.com')) return 'tiktok'
  if (url.includes('pinterest.com')) return 'pinterest'
  if (url.includes('linkedin.com')) return 'linkedin'
  if (url.includes('youtube.com')) return 'youtube'
  if (url.includes('whatsapp') || url.includes('wa.me')) return 'whatsapp'
  return null
}

/**
 * Extract icon name from Font Awesome class
 */
function extractIconFromClass(classIcon: string): string {
  if (!classIcon) return 'globe'
  
  // Extract icon name from classes like "fa fa-instagram" or "fab fa-instagram"
  const match = classIcon.match(/fa-([a-zA-Z0-9-]+)/)
  return match ? match[1] : 'globe'
}
